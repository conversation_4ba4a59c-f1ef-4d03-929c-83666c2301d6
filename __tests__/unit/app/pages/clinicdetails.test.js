import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { MemoryRouter, Route } from 'react-router';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import merge from 'lodash/merge';
import { ToastProvider } from '@app/providers/ToastProvider';
import ClinicDetails from '@app/pages/clinicdetails';

const mockStore = configureStore([thunk]);

describe('ClinicDetails', () => {
  let defaultProps = {
    trackMetric: jest.fn(),
    t: jest.fn().mockImplementation((string) => string),
    api: {
      clinics: {
        getClinicianInvites: jest.fn().mockImplementation((userId, callback) => callback(null, { invitesReturn: 'success' })),
        getClinicsForClinician: jest.fn().mockImplementation((userId, options, callback) => callback(null, [{clinic:{id:'newClinic123'}}])),
        dismissClinicianInvite: jest.fn().mockImplementation((userId, inviteId, callback) => callback(null, { dismissInvite: 'success' })),
        update: jest.fn().mockImplementation((clinicId, updates, callback) => callback(null, { canMigrate: true })),
        create: jest.fn().mockImplementation((clinic, callback) => callback(null, { id: 'newClinic123' })),
        triggerInitialClinicMigration: jest.fn().mockImplementation((clinicId, callback) => callback(null, { triggerMigrationReturn: 'success' })),
        getEHRSettings: jest.fn().mockImplementation((clinicId, callback) => callback(null, { enabled: true })),
        getMRNSettings: jest.fn().mockImplementation((clinicId, callback) => callback(null, { required: true })),
      },
      user: {
        put: jest.fn().mockImplementation((user, callback) => callback(null, { updateUserReturn: 'success' })),
      }
    },
  };

  const defaultWorkingState = {
    inProgress: false,
    completed: false,
    notification: null,
  };

  const workingState = {
    blip: {
      working: {
        fetchingClinicianInvites: defaultWorkingState,
        fetchingClinicsForClinician: defaultWorkingState,
        updatingClinic: defaultWorkingState,
        creatingClinic: defaultWorkingState,
        updatingUser: defaultWorkingState,
        triggeringInitialClinicMigration: defaultWorkingState,
        dismissingClinicianInvite: defaultWorkingState,
      },
    },
  };

  const fetchedWorkingState = {
    blip: {
      working: {
        ...workingState.blip.working,
        fetchingClinicianInvites: {
          ...defaultWorkingState,
          completed: true,
        },
        fetchingClinicsForClinician: {
          ...defaultWorkingState,
          completed: true,
        },
      },
    },
  };

  const defaultState = {
    blip: merge({}, fetchedWorkingState.blip, {
      allUsersMap: {
        clinicianUserId123: {
          emails: ['<EMAIL>'],
          roles: ['clinic'],
          userid: 'clinicianUserId123',
          username: '<EMAIL>',
        },
      },
      clinics: {
        clinicID456: {
          id: 'clinicID456',
          name: 'Clinic 1',
          canMigrate: false,
          clinicians: {
            clinicianUserId123: {
              email: '<EMAIL>',
              id: 'clinicianUserId123',
              roles: ['CLINIC_ADMIN'],
            },
          },
        },
      },
      loggedInUserId: 'clinicianUserId123',
      pendingReceivedClinicianInvites: [],
    }),
  };

  const newClinicianUserInviteState = {
    blip: {
      ...defaultState.blip,
      pendingReceivedClinicianInvites: [
        {
          key: 'invite123',
          creator: { clinicName: 'Example Health' },
        },
      ],
    },
  };

  const initialEmptyClinicState = {
    blip: {
      ...defaultState.blip,
      clinics: {
        clinicID456: {
          id: 'clinicID456',
          name: '',
          canMigrate: false,
          clinicians: {
            clinicianUserId123: {
              email: '<EMAIL>',
              id: 'clinicianUserId123',
              roles: ['CLINIC_ADMIN'],
            },
          },
        },
      },
      selectedClinicId: 'clinicID456',
    },
  };

  const clinicCanMigrateState = {
    blip: {
      ...defaultState.blip,
      allUsersMap: {
        clinicianUserId123: {
          emails: ['<EMAIL>'],
          roles: ['clinic'],
          userid: 'clinicianUserId123',
          username: '<EMAIL>',
          profile: { fullName: 'Clinician One', clinic: { role: 'front_desk' } },
        },
      },
      clinics: {
        clinicID456: {
          id: 'clinicID456',
          name: 'My Clinic',
          canMigrate: true,
          clinicians: {
            clinicianUserId123: {
              email: '<EMAIL>',
              id: 'clinicianUserId123',
              roles: ['CLINIC_ADMIN'],
            },
          },
        },
      },
      selectedClinicId: 'clinicID456',
    },
  };

  let store = mockStore(defaultState);

  const createWrapper = (route = '', providedStore = store) => {
    store = providedStore;

    return render(
      <Provider store={providedStore}>
        <ToastProvider>
          <MemoryRouter initialEntries={[`/clinic-details/${route}`]}>
            <Route path='/clinic-details/:action' children={() => (<ClinicDetails {...defaultProps} />)} />
          </MemoryRouter>
        </ToastProvider>
      </Provider>
    );
  };

  // Mock countries module
  const mockCountries = {
    getNames: jest.fn().mockReturnValue({
      US: 'United States',
      CA: 'Canada',
    }),
    registerLocale: jest.fn(),
    getAlpha2Codes: jest.fn().mockReturnValue({
      US: 'US',
      CA: 'CA',
    }),
  };

  beforeAll(() => {
    // Mock the countries module
    jest.doMock('i18n-iso-countries', () => mockCountries);
  });

  afterAll(() => {
    jest.clearAllMocks();
  });

  beforeEach(() => {
    defaultProps.trackMetric.mockClear();
  });

  afterEach(() => {
    defaultProps.api.clinics.getClinicianInvites.mockClear();
    defaultProps.api.clinics.getClinicsForClinician.mockClear();
    defaultProps.api.clinics.dismissClinicianInvite.mockClear();
    defaultProps.api.clinics.update.mockClear();
    defaultProps.api.clinics.create.mockClear();
    defaultProps.api.clinics.triggerInitialClinicMigration.mockClear();
    defaultProps.api.clinics.getEHRSettings.mockClear();
    defaultProps.api.clinics.getMRNSettings.mockClear();
    defaultProps.api.user.put.mockClear();
  });

  describe('initial fetching', () => {
    beforeEach(() => {
      createWrapper('migrate', mockStore({
        blip: {
          ...defaultState.blip,
          ...workingState.blip,
        },
      }));
    });

    it('should fetch clinician invites and clinics', () => {
      expect(defaultProps.api.clinics.getClinicianInvites).toHaveBeenCalledTimes(1);
      expect(defaultProps.api.clinics.getClinicianInvites).toHaveBeenCalledWith('clinicianUserId123', expect.any(Function));

      expect(defaultProps.api.clinics.getClinicsForClinician).toHaveBeenCalledTimes(1);
      expect(defaultProps.api.clinics.getClinicsForClinician).toHaveBeenCalledWith('clinicianUserId123', expect.any(Object), expect.any(Function));
    });
  });

  describe('content visibility based on route action param', () => {
    it('should render the appropriate form sections for the "/migrate" route', () => {
      createWrapper('migrate', mockStore(defaultState));

      expect(document.querySelector('.container-title')).toBeInTheDocument();
      expect(document.querySelector('.container-subtitle')).toBeInTheDocument();
      expect(document.querySelector('#clinic-invite-details')).not.toBeInTheDocument();
      expect(document.querySelector('#clinician-profile-form')).toBeInTheDocument();
      expect(document.querySelector('#clinic-profile-form')).toBeInTheDocument();
    });

    it('should render the appropriate form sections for the "/profile" route', () => {
      createWrapper('profile', mockStore(defaultState));

      expect(document.querySelector('.container-title')).toBeInTheDocument();
      expect(document.querySelector('.container-subtitle')).toBeInTheDocument();
      expect(document.querySelector('#clinic-invite-details')).not.toBeInTheDocument();
      expect(document.querySelector('#clinician-profile-form')).toBeInTheDocument();
      expect(document.querySelector('#clinic-profile-form')).not.toBeInTheDocument();
    });

    it('should render the appropriate form sections for the "/profile" route with clinic invite present', () => {
      createWrapper('profile', mockStore(newClinicianUserInviteState));

      expect(document.querySelector('.container-title')).toBeInTheDocument();
      expect(document.querySelector('.container-subtitle')).toBeInTheDocument();
      expect(document.querySelector('#clinic-invite-details')).toBeInTheDocument();
      expect(document.querySelector('#clinician-profile-form')).toBeInTheDocument();
      expect(document.querySelector('#clinic-profile-form')).not.toBeInTheDocument();
    });

    it('should render the appropriate form sections for the "/new" route', () => {
      createWrapper('new', mockStore(defaultState));

      expect(document.querySelector('.container-title')).toBeInTheDocument();
      expect(document.querySelector('.container-subtitle')).not.toBeInTheDocument();
      expect(document.querySelector('#clinic-invite-details')).not.toBeInTheDocument();
      expect(document.querySelector('#clinician-profile-form')).not.toBeInTheDocument();
      expect(document.querySelector('#clinic-profile-form')).toBeInTheDocument();
    });
  });

  describe('form submission', () => {
    describe('profile form', () => {
      it('should present a simplified form for updating profile information', async () => {
        const user = userEvent.setup();
        createWrapper('profile', mockStore(newClinicianUserInviteState));

        expect(document.querySelector('div#clinic-profile')).toBeInTheDocument();

        const firstNameInput = document.querySelector('input[name="firstName"]');
        const lastNameInput = document.querySelector('input[name="lastName"]');
        const roleSelect = document.querySelector('select[name="role"]');
        await user.clear(firstNameInput);
        await user.type(firstNameInput, 'Bill');
        expect(firstNameInput.value).toBe('Bill');

        await user.clear(lastNameInput);
        await user.type(lastNameInput, 'Bryerson');
        expect(lastNameInput.value).toBe('Bryerson');

        await user.selectOptions(roleSelect, 'endocrinologist');
        expect(roleSelect.value).toBe('endocrinologist');

        store.clearActions();

        // Find the submit button by text content
        const submitButton = screen.getByRole('button', { name: /next/i });
        expect(submitButton).toBeInTheDocument();

        await user.click(submitButton);

        await waitFor(() => {
          expect(defaultProps.api.user.put).toHaveBeenCalledTimes(1);
        });

        expect(defaultProps.api.user.put).toHaveBeenCalledWith(
          {
            preferences: {},
            profile: {
              clinic: { role: 'endocrinologist' },
              fullName: 'Bill Bryerson'
            },
            roles: ['clinician'],
            userid: 'clinicianUserId123'
          },
          expect.any(Function)
        );

        expect(store.getActions()).toEqual([
          {
            type: 'UPDATE_USER_REQUEST',
            payload: {
              userId: 'clinicianUserId123',
              updatingUser: {
                emails: ['<EMAIL>'],
                roles: ['clinician'],
                userid: 'clinicianUserId123',
                username: '<EMAIL>',
                profile: {
                  fullName: 'Bill Bryerson',
                  clinic: {
                    role: 'endocrinologist',
                  },
                },
                preferences: {},
              },
            },
          },
          {
            type: 'UPDATE_USER_SUCCESS',
            payload: {
              userId: 'clinicianUserId123',
              updatedUser: { updateUserReturn: 'success' },
            },
          },
          {
            type: '@@router/CALL_HISTORY_METHOD',
            payload: {
              method: 'push', args: ['/workspaces', { selectedClinicId: null }],
            }
          }
        ]);
      });
    });

    describe('clinic migration', () => {
      it('should present an expanded form for updating clinician and clinic profile information', async () => {
        const user = userEvent.setup();
        createWrapper('migrate', mockStore(initialEmptyClinicState));

        expect(document.querySelector('div#clinic-profile')).toBeInTheDocument();

        const firstNameInput = document.querySelector('input[name="firstName"]');
        const lastNameInput = document.querySelector('input[name="lastName"]');
        const roleSelect = document.querySelector('select[name="role"]');
        const clinicNameInput = document.querySelector('input[name="name"]');
        const countrySelect = document.querySelector('select[name="country"]');
        const addressInput = document.querySelector('input[name="address"]');
        const cityInput = document.querySelector('input[name="city"]');
        const stateSelect = document.querySelector('select[name="state"]');
        const postalCodeInput = document.querySelector('input[name="postalCode"]');
        const websiteInput = document.querySelector('input[name="website"]');
        const clinicTypeSelect = document.querySelector('select[name="clinicType"]');
        const mmolRadio = document.querySelector('input[name="preferredBgUnits"][value="mmol/L"]');
        const adminAcknowledgeCheckbox = document.querySelector('input[name="adminAcknowledge"]');
        await user.clear(firstNameInput);
        await user.type(firstNameInput, 'Bill');
        expect(firstNameInput.value).toBe('Bill');

        await user.clear(lastNameInput);
        await user.type(lastNameInput, 'Bryerson');
        expect(lastNameInput.value).toBe('Bryerson');

        await user.selectOptions(roleSelect, 'endocrinologist');
        expect(roleSelect.value).toBe('endocrinologist');

        await user.clear(clinicNameInput);
        await user.type(clinicNameInput, 'My Clinic');
        expect(clinicNameInput.value).toBe('My Clinic');

        await user.selectOptions(countrySelect, 'US');
        expect(countrySelect.value).toBe('US');

        await user.clear(addressInput);
        await user.type(addressInput, '253 Mystreet Ave Apt. 34');
        expect(addressInput.value).toBe('253 Mystreet Ave Apt. 34');

        await user.clear(cityInput);
        await user.type(cityInput, 'Gotham');
        expect(cityInput.value).toBe('Gotham');

        await user.selectOptions(stateSelect, 'NJ');
        expect(stateSelect.value).toBe('NJ');

        await user.clear(postalCodeInput);
        await user.type(postalCodeInput, '90210');
        expect(postalCodeInput.value).toBe('90210');

        await user.clear(websiteInput);
        await user.type(websiteInput, 'http://clinic.com');
        expect(websiteInput.value).toBe('http://clinic.com');

        await user.selectOptions(clinicTypeSelect, 'healthcare_system');
        expect(clinicTypeSelect.value).toBe('healthcare_system');

        await user.click(mmolRadio);
        expect(mmolRadio.checked).toBe(true);

        await user.click(adminAcknowledgeCheckbox);
        expect(adminAcknowledgeCheckbox.checked).toBe(true);

        store.clearActions();

        // Debug: log all buttons to see what's available
        const allButtons = screen.getAllByRole('button');
        console.log('Available buttons:', allButtons.map(btn => btn.textContent));

        // Find the submit button by text content
        const submitButton = screen.getByRole('button', { name: /create workspace/i });
        expect(submitButton).toBeInTheDocument();

        await user.click(submitButton);

        await waitFor(() => {
          expect(defaultProps.api.user.put).toHaveBeenCalledTimes(1);
        });

        expect(defaultProps.api.user.put).toHaveBeenCalledWith(
          {
            preferences: {},
            profile: {
              clinic: { role: 'endocrinologist' },
              fullName: 'Bill Bryerson'
            },
            roles: ['clinician'],
            userid: 'clinicianUserId123'
          },
          expect.any(Function)
        );

        expect(defaultProps.api.clinics.update).toHaveBeenCalledTimes(1);

        expect(defaultProps.api.clinics.update).toHaveBeenCalledWith(
          'clinicID456',
          {
            address: '253 Mystreet Ave Apt. 34',
            city: 'Gotham',
            clinicType: 'healthcare_system',
            country: 'US',
            name: 'My Clinic',
            postalCode: '90210',
            state: 'NJ',
            website: 'http://clinic.com',
            preferredBgUnits: 'mmol/L',
          },
          expect.any(Function)
        );

        expect(store.getActions()).toEqual([
          {
            type: 'UPDATE_USER_REQUEST',
            payload: {
              userId: 'clinicianUserId123',
              updatingUser: {
                emails: ['<EMAIL>'],
                roles: ['clinician'],
                userid: 'clinicianUserId123',
                username: '<EMAIL>',
                profile: {
                  fullName: 'Bill Bryerson',
                  clinic: {
                    role: 'endocrinologist',
                  },
                },
                preferences: {},
              },
            },
          },
          {
            type: 'UPDATE_USER_SUCCESS',
            payload: {
              userId: 'clinicianUserId123',
              updatedUser: { updateUserReturn: 'success' },
            },
          },
          { type: 'UPDATE_CLINIC_REQUEST' },
          {
            type: 'UPDATE_CLINIC_SUCCESS',
            payload: {
              clinicId: 'clinicID456',
              clinic: { canMigrate: true },
            },
          },
        ]);
      });
    });

    describe('clinic creation', () => {
      it('should present an expanded form for adding new clinic profile information', async () => {
        const user = userEvent.setup();
        createWrapper('new', mockStore(defaultState));

        expect(document.querySelector('div#clinic-profile')).toBeInTheDocument();

        const clinicNameInput = document.querySelector('input[name="name"]');
        const countrySelect = document.querySelector('select[name="country"]');
        const addressInput = document.querySelector('input[name="address"]');
        const cityInput = document.querySelector('input[name="city"]');
        const stateSelect = document.querySelector('select[name="state"]');
        const postalCodeInput = document.querySelector('input[name="postalCode"]');
        const websiteInput = document.querySelector('input[name="website"]');
        const clinicTypeSelect = document.querySelector('select[name="clinicType"]');
        const mmolRadio = document.querySelector('input[name="preferredBgUnits"][value="mmol/L"]');
        const adminAcknowledgeCheckbox = document.querySelector('input[name="adminAcknowledge"]');
        await user.tripleClick(clinicNameInput);
        await user.type(clinicNameInput, 'My Clinic');
        expect(clinicNameInput.value).toBe('My Clinic');

        await user.selectOptions(countrySelect, 'US');
        expect(countrySelect.value).toBe('US');

        await user.clear(addressInput);
        await user.type(addressInput, '253 Mystreet Ave Apt. 34');
        expect(addressInput.value).toBe('253 Mystreet Ave Apt. 34');

        await user.clear(cityInput);
        await user.type(cityInput, 'Gotham');
        expect(cityInput.value).toBe('Gotham');

        await user.selectOptions(stateSelect, 'NJ');
        expect(stateSelect.value).toBe('NJ');

        await user.clear(postalCodeInput);
        await user.type(postalCodeInput, '90210');
        expect(postalCodeInput.value).toBe('90210');

        await user.clear(websiteInput);
        await user.type(websiteInput, 'http://clinic.com');
        expect(websiteInput.value).toBe('http://clinic.com');

        await user.selectOptions(clinicTypeSelect, 'healthcare_system');
        expect(clinicTypeSelect.value).toBe('healthcare_system');

        await user.click(mmolRadio);
        expect(mmolRadio.checked).toBe(true);

        await user.click(adminAcknowledgeCheckbox);
        expect(adminAcknowledgeCheckbox.checked).toBe(true);

        store.clearActions();

        // Find the submit button by text content
        const submitButton = screen.getByRole('button', { name: /create workspace/i });
        expect(submitButton).toBeInTheDocument();

        await user.click(submitButton);

        await waitFor(() => {
          expect(defaultProps.api.clinics.create).toHaveBeenCalledTimes(1);
        });

        expect(defaultProps.api.user.put).not.toHaveBeenCalled();

        expect(defaultProps.api.clinics.create).toHaveBeenCalledWith(
          {
            address: '253 Mystreet Ave Apt. 34',
            city: 'Gotham',
            clinicType: 'healthcare_system',
            country: 'US',
            name: 'My Clinic',
            postalCode: '90210',
            state: 'NJ',
            website: 'http://clinic.com',
            preferredBgUnits: 'mmol/L',
          },
          expect.any(Function)
        );

        const expectedActions = [
          { type: 'CREATE_CLINIC_REQUEST' },
          {
            type: 'CREATE_CLINIC_SUCCESS',
            payload: {
              clinic: { id: 'newClinic123' },
            },
          },
          { type: 'SELECT_CLINIC_SUCCESS', payload: { clinicId: 'newClinic123' } },
          { type: 'GET_CLINICS_FOR_CLINICIAN_REQUEST' },
          { type: 'GET_CLINICS_FOR_CLINICIAN_SUCCESS', payload: { clinics: [ { clinic: { id: 'newClinic123' } } ], clinicianId: 'clinicianUserId123' } },
          { type: 'FETCH_CLINIC_EHR_SETTINGS_REQUEST' },
          { type: 'FETCH_CLINIC_EHR_SETTINGS_SUCCESS', payload: { clinicId: 'newClinic123', settings: {enabled: true} } },
          { type: 'FETCH_CLINIC_MRN_SETTINGS_REQUEST' },
          { type: 'FETCH_CLINIC_MRN_SETTINGS_SUCCESS', payload: { clinicId: 'newClinic123', settings: {required: true} } },
        ];

        const actions = store.getActions();
        expect(actions).toEqual(expectedActions);
      });
    });

    describe('pre-populate clinic team member profile fields', () => {
      it('should not populate the team member profile fields if the clinic details have not been filled out', () => {
        createWrapper('profile', mockStore(initialEmptyClinicState));

        const firstNameInput = document.querySelector('input[name="firstName"]');
        const lastNameInput = document.querySelector('input[name="lastName"]');
        const roleSelect = document.querySelector('select[name="role"]');

        expect(firstNameInput.value).toBe('');
        expect(lastNameInput.value).toBe('');
        expect(roleSelect.value).toBe('');
      });

      it('should populate the team member profile fields if the clinic details have been filled out', () => {
        createWrapper('migrate', mockStore(clinicCanMigrateState));

        const firstNameInput = document.querySelector('input[name="firstName"]');
        const lastNameInput = document.querySelector('input[name="lastName"]');
        const roleSelect = document.querySelector('select[name="role"]');

        expect(firstNameInput.value).toBe('Clinician');
        expect(lastNameInput.value).toBe('One');
        expect(roleSelect.value).toBe('front_desk');
      });
    });

    describe('clinic is ready to migrate on load', () => {
      it('should open the migration confirmation modal', () => {
        createWrapper('migrate', mockStore(clinicCanMigrateState));

        const confirmMigrationDialog = document.querySelector('[role="dialog"]');
        expect(confirmMigrationDialog).toBeInTheDocument();
      });
    });
  });
});
